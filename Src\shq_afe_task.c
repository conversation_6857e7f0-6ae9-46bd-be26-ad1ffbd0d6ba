#include <stdint.h>
#include <string.h>
#include <stdlib.h>

#include "shq_afe.h"
#include "platform/comm_io.h"
#include "shq89718/shq89xx.h"
#include "shq_afe_task.h"
#include "shq_timer.h"
#include "shq89718/shq89718_registers.h"

// 简化的任务数据结构
static SHQ_AFE_DATA g_afe_data;
static int8_t g_afe_running = 0;
static uint8_t enable_flag = 0;
static uint8_t delay_ret[50];
static uint8_t delay_index = 0;
static int8_t afe_initialized = 0;

// 简化的命令处理函数
int32_t cmd_proc(uint32_t cmd, uint8_t *buf, int32_t buf_size, int8_t *rep, uint32_t argc, uint8_t *argv[])
{
    // 简化命令处理，只保留基本的启停控制
    if (cmd == '0dmc') {
        g_afe_running = 0;  // 停止
        return 0;
    } else if (cmd == '1dmc') {
        g_afe_running = 1;  // 启动
        return 0;
    } else if (cmd == 'rdmc') {
        // 重启AFE系统
        shq_afe_simple_restart();
        return 0;
    } else if (cmd == 'yldt') {
        // SPI测试延时配置命令: tdly <cs_clock_us> <byte_us> <cmd_us> <enable>
        // 注意：输入tdly，接收到的是yldt (字符串颠倒)
        // 示例: tdly 10 5 100 1  (片选时钟间延时10us, 字节间延时5us, 命令间延时100us, 启用)
        if (argc >= 4) {
            uint32_t cs_clock_delay = 0;
            uint32_t byte_delay = 0;
            uint32_t cmd_delay = 0;
            uint8_t enable = 0;

            // 解析参数
            if (argv[0]) cs_clock_delay = atoi((char*)argv[0]);
            if (argv[1]) byte_delay = atoi((char*)argv[1]);
            if (argv[2]) cmd_delay = atoi((char*)argv[2]);
            if (argv[3]) enable = atoi((char*)argv[3]);

            // 限制延时范围 (最大10ms = 10000us)
            if (cs_clock_delay > 10000) cs_clock_delay = 10000;
            if (byte_delay > 10000) byte_delay = 10000;
            if (cmd_delay > 10000) cmd_delay = 10000;

            // 设置测试延时配置
            g_spi_test_delays.cs_clock_delay_us = cs_clock_delay;
            g_spi_test_delays.byte_delay_us = byte_delay;
            g_spi_test_delays.cmd_delay_us = cmd_delay;
            enable_flag = enable;

            print("inf: SPI测试延时配置: CS-CLK=%dus, BYTE=%dus, CMD=%dus, EN=%d\n",
                  cs_clock_delay, byte_delay, cmd_delay, enable);
             g_afe_running = 1;  // 启动
            return 0;
        } else {
            print("err: tdly命令参数不足，需要4个参数: <cs_clock_us> <byte_us> <cmd_us> <enable>\n");
            return -1;
        }
    } else if (cmd == 'yldq') {
        // 查询当前SPI测试延时配置
        // 注意：输入qdly，接收到的是yldq (字符串颠倒)
        print("inf: 当前SPI测试延时配置:\n");
        print("inf: 片选-时钟延时: %d us\n", g_spi_test_delays.cs_clock_delay_us);
        print("inf: 字节间延时: %d us\n", g_spi_test_delays.byte_delay_us);
        print("inf: 命令间延时: %d us\n", g_spi_test_delays.cmd_delay_us);
        print("inf: 测试延时状态: %s\n", enable_flag ? "启用" : "禁用");
        return 0;
    } else if (cmd == 'terq') {
        print("inf: SPI测试结果: ");
        for (int i = 0; i < delay_index; i++) {
            print("0x%02x, ", delay_ret[i]);
        }
        return 0;
    }

    return -1; // 命令未处理
}

static const uint8_t afe_cell_counts[] = {18, 18, 18, 18, 18, 18, 18, 18};	 // cell count for each stack
static const uint8_t afe_bus_bar[] = {100, 100, 100, 100, 100, 100, 100, 100}; // bus bar position
static const uint8_t afe_gp_ch_counts[] = {11, 11, 11, 11, 11, 11, 11, 11};

extern void shq89718_global_init(void);

// 简化的初始化函数
void shq_afe_task_init(void)
{
    // 清零数据结构
    memset(&g_afe_data, 0, sizeof(g_afe_data));
    
    memset(delay_ret, 0, sizeof(delay_ret));
    delay_index = 0;

    // 设置基本配置
    g_afe_data.stack_count = STACK_COUNT;
    g_afe_data.cell_counts = afe_cell_counts;
    g_afe_data.bus_bar_pos = afe_bus_bar;
    g_afe_data.gp_ch_counts = afe_gp_ch_counts;

    // 初始化诊断专用缓存区
    memset(g_afe_data.diag_volts_vc, 0, sizeof(g_afe_data.diag_volts_vc));
    memset(g_afe_data.diag_volts_cb, 0, sizeof(g_afe_data.diag_volts_cb));
    memset(g_afe_data.diag_volts_gp, 0, sizeof(g_afe_data.diag_volts_gp));

    // 初始化底层全局设置
    shq89718_global_init();

    // 设置为停止状态
    g_afe_running = 0;
}

// 简化的重启函数
void shq_afe_simple_restart(void)
{
    // 清除错误状态
    g_afe_data.stack_err_addr_fwd = 0;
    g_afe_data.stack_err_addr_bwd = 0;
    g_afe_data.stack_count_fwd = 0;
    g_afe_data.stack_count_bwd = 0;
    g_afe_data.comm_dir_state = 0;
    g_afe_data.dsc_stack_comm_err = 0;

    // 清零诊断专用缓存区
    memset(g_afe_data.diag_volts_vc, 0, sizeof(g_afe_data.diag_volts_vc));
    memset(g_afe_data.diag_volts_cb, 0, sizeof(g_afe_data.diag_volts_cb));
    memset(g_afe_data.diag_volts_gp, 0, sizeof(g_afe_data.diag_volts_gp));

    // 停止运行
    g_afe_running = 0;
}

// 简化的AFE唤醒序列
int32_t shq_afe_simple_wakeup(int8_t dir)
{
    int32_t ret;

    print("inf: 开始AFE唤醒序列\n");

    // 1. 唤醒基础设备
    print("inf: 1. 唤醒基础设备\n");
    ret = shq_base_wakeup();
    if (ret < 0) {
        print("err: 基础设备唤醒失败: %d\n", ret);
        return ret;
    }

    // 2. 基础配置
    print("inf: 2. 基础设备配置\n");
    ret = shq_base_config(dir);
    if (ret < 0) {
        print("err: 基础设备配置失败: %d\n", ret);
        return ret;
    }

    // 3. 唤醒堆栈设备 - 正向
    print("inf: 3. 唤醒堆栈设备\n");
    ret = shq_stack_wakeup((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 堆栈设备唤醒失败: %d\n", ret);
        return ret;
    }

    // 4. 地址分配 - 正向
    print("inf: 4. 地址分配\n");
    ret = shq_addressing((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 地址分配失败: %d\n", ret);
        return ret;
    }

    // 5. 初始化设备
    print("inf: 5. 设备初始化\n");
    ret = shq_init((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 设备初始化失败: %d\n", ret);
        return ret;
    }

    print("inf: AFE唤醒序列完成\n");
    return 0;
}

// 简化的采样函数
int32_t shq_afe_simple_sampling(int8_t dir)
{
    int32_t ret;

    // 电芯电压采样
    print("inf: 开始电芯电压采样\n");
    ret = shq_cs_sampling((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 电芯电压采样失败: %d\n", ret);
        return ret;
    }

    // GPIO温度采样
    print("inf: 开始GPIO温度采样\n");
    ret = shq_gp_sampling((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: GPIO温度采样失败: %d\n", ret);
        return ret;
    }

    // 断线诊断
    print("inf: 开始断线诊断\n");
    ret = diag_cell_wrie_disconnection((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 断线诊断失败: %d\n", ret);
        return ret;
    }

    print("inf: 采样完成\n");
    return 0;
}


int32_t spi_test(void){
    // 覆盖single/stack read/write 写加回读验证结果
    uint8_t *p_ret;
    uint8_t ret = 0x00;
    uint8_t reg_addr = 0x008;
    un_SPI_TX2_t spi_tx_data;
    spi_tx_data.stcField.DATA = 0;
    uint8_t reg_addr2 = 0x009;
    un_SPI_TX1_t spi_tx_data2;
    spi_tx_data2.stcField.DATA = 0;

    spi_tx_data2.stcField.DATA = 0x05;
    single_dev_write(1, reg_addr2, &spi_tx_data2.u8Register, 1);
    spi_tx_data.stcField.DATA = 0x50;
    single_dev_write(1, reg_addr, &spi_tx_data.u8Register, 1);
    single_dev_read(1, reg_addr, 1, &p_ret);
    p_ret += DSC_FRAME_DATA_OFFSET;
    if (p_ret[0] != spi_tx_data.stcField.DATA) {
        print("err: single_dev_read failed, device%d expect 0x%02X, got 0x%02X\n", 1, spi_tx_data.stcField.DATA, p_ret[0]);  // 设备1
        ret|=0x1;
    }
    spi_tx_data2.stcField.DATA = 0x0a;
    single_dev_write(2, reg_addr2, &spi_tx_data2.u8Register, 1);
    spi_tx_data.stcField.DATA = 0xa0;
    single_dev_write(2, reg_addr, &spi_tx_data.u8Register, 1);
    single_dev_read(2, reg_addr, 1, &p_ret);
    p_ret += DSC_FRAME_DATA_OFFSET;
    if (p_ret[0] != spi_tx_data.stcField.DATA) {
        print("err: single_dev_read failed, device%d expect 0x%02X, got 0x%02X\n", 2, spi_tx_data.stcField.DATA, p_ret[0]);  // 设备2
        ret|=0x2;
    }

    spi_tx_data2.stcField.DATA = 0x5a;
    stack_write(reg_addr2, &spi_tx_data2.u8Register, 1);
    spi_tx_data.stcField.DATA = 0xa5;
    stack_write(reg_addr, &spi_tx_data.u8Register, 1);
    stack_read(reg_addr, 1, 2, &p_ret);
    p_ret += (1 + DSC_RESP_NONE_DATA_BYTES) + DSC_FRAME_DATA_OFFSET;
    if (p_ret[0] != spi_tx_data.stcField.DATA) {
        print("err: stack_read failed 1, expect 0x%02X, got 0x%02X\n", spi_tx_data.stcField.DATA, p_ret[0]);
        ret|=0x4;
    }
    p_ret -= (1 + DSC_RESP_NONE_DATA_BYTES);
    if (p_ret[0] != spi_tx_data.stcField.DATA) {
        print("err: stack_read failed 2, expect 0x%02X, got 0x%02X\n", spi_tx_data.stcField.DATA, p_ret[0]);
        ret|=0x8;
    }

    print("inf: SPI测试完成: 0x%02x\n", ret);
    delay_ret[delay_index++] = ret;
    return ret;
}

// 简化的掉电函数
int32_t shq_afe_simple_power_down(void)
{
    int32_t ret;

    print("inf: 开始AFE系统掉电\n");

    // 停止AFE运行
    g_afe_running = 0;

    // 执行掉电序列
    ret = shq_power_down();
    if (ret < 0) {
        print("err: AFE系统掉电失败: %d\n", ret);
        return ret;
    }

    print("inf: AFE系统掉电完成\n");
    return 0;
}


void start_xup_transfer() {
    uint8_t header[12] = {0};
    int32_t total_bytes = 12 + delay_index;
    
    *((uint32_t *)header) = 0xDDDFFD;
    header[3] = total_bytes/4;
    *((uint32_t *)&header[4]) = 0x00;
    *((uint32_t *)&header[7]) = 0x01;
    *((uint32_t *)&header[8]) = 0x0;
    hal->vc_tx(sizeof(header), header);
}


void process_data_transfer() {
    start_xup_transfer();

    int32_t value_total_bytes = delay_index;
    if (value_total_bytes > 50) {
        value_total_bytes = 50;
    }

    uint8_t header_value[4] = {0};
    header_value[3] = 0xaa;
    header_value[2] = 0xaa;
    header_value[1] = 0xaa;
    header_value[0] = value_total_bytes;
    hal->vc_tx(sizeof(header_value), header_value);
    hal->vc_tx(value_total_bytes, delay_ret);
}


// 简化的主运行函数
int32_t shq_afe_task_run(void)
{
    
    int32_t ret;
    int8_t dir = 0;

    // 如果没有运行，直接返回
    if (!g_afe_running) {
        // afe_initialized = 0;
        return 0;
    }

    // 如果还没初始化，先进行唤醒和初始化
    if (!afe_initialized) {
        print("inf: 开始唤醒和初始化\n");
        ret = shq_afe_simple_wakeup(dir);
        if (ret < 0) {
            // 初始化失败，等待一段时间后重试
            simple_delay_ms(500);
            return ret;
        }
        afe_initialized = 1;
    }

    // 执行采样
    // print("inf: 开始采样\n");
    // ret = shq_afe_simple_sampling(dir);
    // if (ret < 0) {
    //     // 采样失败，重新初始化
    //     afe_initialized = 0;
    //     return ret;
    // }

    if (enable_flag) {
        simple_delay_ms(10);
        // spi测试
        print("inf: 开始SPI测试\n");
        g_spi_test_delays.enable_test_delays = 1;
        ret = spi_test();
        if (ret != 0) {
            // 测试失败，重新初始化
            afe_initialized = 0;
            print("err: SPI测试失败: %d\n", ret);
        }
        g_spi_test_delays.enable_test_delays = 0;
        enable_flag = 0;
        g_afe_running = 0;
        // process_data_transfer();
        // simple_delay_ms(1000);
    }

    // 掉电
    // print("inf: 开始AFE系统掉电\n");
    // ret = shq_afe_simple_power_down();
    // if (ret < 0) {
    //     // 掉电失败，等待一段时间后重试
    //     simple_delay_ms(500);
    //     return ret;
    // }
    // afe_initialized = 0;

    // print("inf: 等待下个指令\n");
    // simple_delay_ms(1000); // 等待下个指令，1秒钟后开始下一轮循环，可以根据实际情况调整延迟时间
    
    return 0;
}

