#include <stdint.h>
#include <string.h>
#include "comm_io.h"
#include "../shq_timer.h"
#include "../shq_afe.h"

#ifdef STM32

#if defined(STM32F401xC) || defined(STM32F401RCTx)
#include "stm32f4xx_hal_conf.h"
#endif

#ifdef STM32L433xx
#include "stm32l4xx_hal_conf.h"
#endif

#ifdef STM32G431xx
#include "stm32g4xx_hal_conf.h"
#endif

const STDHAL *hal = (const STDHAL*)0x800FC00;

// 全局测试延时配置实例
spi_test_delays_t g_spi_test_delays = {0, 0, 0, 0};

// Delays execution for a specified number of microseconds.
void delay_us(uint32_t useconds)
{
	hal->delay_us(useconds);
}

// 测试延时函数 - 在片选和时钟之间插入延时
static void test_delay_cs_clock(void)
{
    if (g_spi_test_delays.enable_test_delays && g_spi_test_delays.cs_clock_delay_us > 0) {
        delay_us(g_spi_test_delays.cs_clock_delay_us);
    }
}

// 测试延时函数 - 在字节之间插入延时
static void test_delay_between_bytes(void)
{
    if (g_spi_test_delays.enable_test_delays && g_spi_test_delays.byte_delay_us > 0) {
        delay_us(g_spi_test_delays.byte_delay_us);
    }
}

// 测试延时函数 - 在命令之间插入延时
static void test_delay_between_commands(void)
{
    if (g_spi_test_delays.enable_test_delays && g_spi_test_delays.cmd_delay_us > 0) {
        delay_us(g_spi_test_delays.cmd_delay_us);
    }
}

// Controls the chip select (CS) line for SPI communication.
// Sets the CS pin high or low based on the provided level.
static int32_t spi_cs(uint8_t level)
{
	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, level ? GPIO_PIN_SET : GPIO_PIN_RESET);
	return 0;
}

// Transmits data over SPI.
// Takes a pointer to the data buffer and the number of bytes to send.
// Returns the number of bytes sent, or 0 if the transmission failed.
static int32_t spi_tx(uint8_t *tx_buf, uint32_t bytes_to_send)
{
	SPI_HandleTypeDef *pSpiHandles = (SPI_HandleTypeDef *)hal->spi_instances;

	// 如果启用了字节间延时测试，逐字节发送
	if (g_spi_test_delays.enable_test_delays && g_spi_test_delays.byte_delay_us > 0 && bytes_to_send > 1) {
		uint32_t sent_bytes = 0;
		for (uint32_t i = 0; i < bytes_to_send; i++) {
			if (HAL_OK == HAL_SPI_Transmit(&pSpiHandles[0], &tx_buf[i], 1, 100)) {
				sent_bytes++;
				if (i < bytes_to_send - 1) {  // 不在最后一个字节后延时
					test_delay_between_bytes();
				}
			} else {
				return sent_bytes;
			}
		}
		return sent_bytes;
	} else {
		// 正常发送
		if (HAL_OK == HAL_SPI_Transmit(&pSpiHandles[0], tx_buf, bytes_to_send, 100)) {
			return bytes_to_send;
		}
		return 0;
	}
}

// Receives data over SPI.
// Sends a dummy buffer while receiving the response into the provided buffer.
// Returns the number of bytes received, or 0 if the operation failed.
static int32_t spi_rx(uint8_t *rx_buf, uint32_t bytes_to_receive)
{
	uint8_t tx_buf[256];
	memset(tx_buf, 0xff, 256); // Keeps MOSI high when reading.
	SPI_HandleTypeDef *pSpiHandles = (SPI_HandleTypeDef *)hal->spi_instances;

	// 如果启用了字节间延时测试，逐字节接收
	if (g_spi_test_delays.enable_test_delays && g_spi_test_delays.byte_delay_us > 0 && bytes_to_receive > 1) {
		uint32_t received_bytes = 0;
		for (uint32_t i = 0; i < bytes_to_receive; i++) {
			if (HAL_OK == HAL_SPI_TransmitReceive(&pSpiHandles[0], &tx_buf[i], &rx_buf[i], 1, 100)) {
				received_bytes++;
				if (i < bytes_to_receive - 1) {  // 不在最后一个字节后延时
					test_delay_between_bytes();
				}
			} else {
				return received_bytes;
			}
		}
		return received_bytes;
	} else {
		// 正常接收
		if (HAL_OK == HAL_SPI_TransmitReceive(&pSpiHandles[0], tx_buf, rx_buf, bytes_to_receive, 100)) {
			return bytes_to_receive;
		}
		return 0;
	}
}

// Checks if the device is ready by reading SPI_RDY pin.
int32_t dev_is_ready()
{
	return HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_1);
}

// Task executed every 1 ms to handle long pulse timing and timer counter.
static uint8_t long_pulse_task = 0;
void shq_systick_task(void)
{
	on_system_tick(); // Call the timer function.

	if (long_pulse_task) {
		if (!(long_pulse_task & 0x80)) {
			long_pulse_task --;
			if (!long_pulse_task) {
				hal->gpio_write(0, GPIO_PIN_7, 1); // set MOSI to high at end
			}
		} else {
			hal->gpio_write(0, GPIO_PIN_7, 0); // set MOSI to low to start
			long_pulse_task &= 0x7f;
		}
	}
}

// Starts a long pulse of specified width in milliseconds.
int32_t dev_start_long_pulse(uint8_t width_ms)
{
#ifdef STM32
	hal->gpio_config(0, GPIO_PIN_7, GPIO_MODE_OUTPUT_PP, /* value */ 1, GPIO_NOPULL, /* alt */ 0, GPIO_SPEED_FREQ_LOW);
	delay_us(1);
	spi_cs(0);
	delay_us(1);
	long_pulse_task = 0x80 | width_ms;
#endif
	return 0;
}

int32_t dev_is_long_pulse_done()
{
    if (long_pulse_task) {
        return 0; // Long pulse is still ongoing.
    } else {
#ifdef STM32
		hal->gpio_write(0, GPIO_PIN_7, 1); // set MOSI to high at end
		delay_us(1);
		spi_cs(1);
		hal->gpio_config(0, GPIO_PIN_7, GPIO_MODE_AF_PP, /* value */ 1, GPIO_NOPULL, /* alt */ GPIO_AF5_SPI1, GPIO_SPEED_FREQ_HIGH);
#endif
        return 1; // Long pulse is done.
    }
}

// Sends a pulse of specified width in microseconds. use delay_us or SPI transmit
int32_t dev_send_pulse(uint32_t width_us)
{
#ifdef STM32
	hal->gpio_config(0, GPIO_PIN_7, GPIO_MODE_OUTPUT_PP, /* value */ 1, GPIO_NOPULL, /* alt */ 0, GPIO_SPEED_FREQ_LOW);
	delay_us(1);
	spi_cs(0);
	delay_us(1);
	hal->gpio_write(0, GPIO_PIN_7, 0);
	delay_us(width_us);
	hal->gpio_write(0, GPIO_PIN_7, 1);
	delay_us(1);
	spi_cs(1);
	delay_us(1);
	hal->gpio_config(0, GPIO_PIN_7, GPIO_MODE_AF_PP, /* value */ 1, GPIO_NOPULL, /* alt */ GPIO_AF5_SPI1, GPIO_SPEED_FREQ_HIGH);
#else

#define PULSE_TX_BYTE_WIDTH 75 // 7.5 uS for one byte, platform dependent
#define PULSE_TX_BLOCK_SIZE 256

	uint8_t tx_buf[PULSE_TX_BLOCK_SIZE];
	int32_t blocks_to_send = (width_us * 10 / PULSE_TX_BYTE_WIDTH) / PULSE_TX_BLOCK_SIZE;
	int32_t bytes_to_send = width_us * 10 / PULSE_TX_BYTE_WIDTH - PULSE_TX_BLOCK_SIZE * blocks_to_send;

	uint16_t i;

	tx_buf[0] = 0xff;
	spi_tx(tx_buf, 1); // make the MOSI to high

	spi_cs(0);
	memset(tx_buf, 0, PULSE_TX_BLOCK_SIZE);

	delay_us(6);
	for (i = 0; i < blocks_to_send; i++)
	{
		spi_tx(tx_buf, PULSE_TX_BLOCK_SIZE);
	}
	if (bytes_to_send > 0)
	{
		spi_tx(tx_buf, bytes_to_send);
	}
	tx_buf[0] = 0xff;
	spi_tx(tx_buf, 1); // make the MOSI to high
	spi_cs(1);
#endif
	return 0;
}

// Transmits data over SPI with chip select management.
static int32_t shq_spi_tx(uint8_t *tx_buf, uint32_t bytes_to_send)
{
	int32_t ret;
	spi_cs(0);
	test_delay_cs_clock();  // 片选和时钟之间的测试延时
	ret = spi_tx(tx_buf, bytes_to_send);
	test_delay_cs_clock();  // 片选和时钟之间的测试延时
	spi_cs(1);
	test_delay_between_commands();  // 命令之间的测试延时
	return ret;
}

// Receives data over SPI with chip select management.
static int32_t shq_spi_rx(uint8_t *rx_buf, uint32_t bytes_to_receive)
{
	int32_t ret;
	spi_cs(0);
	test_delay_cs_clock();  // 片选和时钟之间的测试延时
	ret = spi_rx(rx_buf, bytes_to_receive);
	test_delay_cs_clock();  // 片选和时钟之间的测试延时
	spi_cs(1);
	test_delay_between_commands();  // 命令之间的测试延时
	return ret;
}

// Clears the communication by sending a dummy byte.
int32_t dev_comm_clear()
{
	uint8_t data = 0;
	int32_t ret = shq_spi_tx(&data, 1);
	delay_us(1);
	return ret;
}

// Handles SPI transactions with optional data transmission and reception.
int32_t dev_spi_trans(uint8_t *data_tx, uint32_t numTxBytes, uint8_t *data_rx, uint32_t numRxFrameBytes, uint32_t numRxFrames, int32_t usDelayBeforeRead)
{
	int32_t tmo = 1000;
	while (tmo > 0 && !dev_is_ready())
	{
		delay_us(10);
		tmo -= 10;
	}
	if (tmo <= 0)
	{
		return COMME_TRANS_BUSY; // as the comm clear needs more time to restore, not processed here
	}
	if (numTxBytes > 0&&numTxBytes < 20)
	{
		if(numTxBytes > 20){
			uint32_t segment_size = numTxBytes / 4;
			for (uint32_t i = 0; i < 4; ++i) {
				shq_spi_tx(data_tx+ i * segment_size, segment_size);

			delay_us(100+segment_size * 10);}}
		shq_spi_tx(data_tx, numTxBytes);
		delay_us( 100+numTxBytes *  10);
	}
	if (numRxFrameBytes > 0)
	{
		uint32_t bytes_to_read = numRxFrameBytes * numRxFrames;
		uint32_t bytes_read = 0;
		uint8_t *p_data_rx = data_rx;

		if (bytes_to_read > DSC_COMM_BUFFER_SIZE) {
			print("inf:transfer buffer too small.");
			return COMME_FAILED;
		}

		if (usDelayBeforeRead) {
			delay_us(usDelayBeforeRead);
		}

		tmo = SHQ_DSC_MAX_TMO_USEC; // 1.85*2
		while (tmo > 0 && !dev_is_ready())
		{
			delay_us(10);
			tmo -= 10;
		}
		if (tmo <= 0)
		{
			return COMME_TRANS_TIMEOUT;
		}

		while (bytes_to_read >= 128) {
			shq_spi_rx(p_data_rx, 128);
			bytes_read += 128;
			p_data_rx += 128;
			bytes_to_read -= 128;
			if (bytes_to_read) {
				// wait rdy
				tmo = SHQ_DSC_MAX_TMO_USEC;
				while (tmo > 0 && !dev_is_ready())
				{
					delay_us(10);
					tmo -= 10;
				}
				if (tmo <= 0)
				{
					return COMME_TRANS_TIMEOUT;
				}
			}
		}
		shq_spi_rx(p_data_rx, bytes_to_read);
		return (bytes_read + bytes_to_read);
	}
	return COMME_OK;
}

int32_t dev_spi_trans_times(uint8_t *data_tx, uint32_t numTxBytes, uint8_t *data_rx, uint32_t numRxFrameBytes, uint32_t numRxFrames, int32_t usDelayBeforeRead,uint8_t* call_last)
{
	int32_t tmo = 1000;
	uint8_t last_times = *call_last;
	while (tmo > 0 && !dev_is_ready())
	{
		delay_us(10);
		tmo -= 10;
	}
	if (tmo <= 0)
	{
		return COMME_TRANS_BUSY; // as the comm clear needs more time to restore, not processed here
	}
	if (numTxBytes > 0)
	{

		uint32_t segment_size = numTxBytes / last_times;
		for (uint32_t i = 0; i < last_times; ++i) {
			shq_spi_tx(data_tx+ i * segment_size, segment_size);

		delay_us(100+segment_size * 10);}

		}
		if (numRxFrameBytes > 0)
		{
			uint32_t bytes_to_read = numRxFrameBytes * numRxFrames;
			uint32_t bytes_read = 0;
			uint8_t *p_data_rx = data_rx;

			if (bytes_to_read > DSC_COMM_BUFFER_SIZE) {
				print("inf:transfer buffer too small.");
				return COMME_FAILED;
			}

			if (usDelayBeforeRead) {
				delay_us(usDelayBeforeRead);
			}
			tmo = SHQ_DSC_MAX_TMO_USEC; // 1.85*2
			while (tmo > 0 && !dev_is_ready())
			{
				delay_us(10);
				tmo -= 10;
			}
			if (tmo <= 0)
			{
				return COMME_TRANS_TIMEOUT;
			}
			while (bytes_to_read >= 128) {
				shq_spi_rx(p_data_rx, 128);
				bytes_read += 128;
				p_data_rx += 128;
				bytes_to_read -= 128;
				if (bytes_to_read) {
					// wait rdy
					tmo = SHQ_DSC_MAX_TMO_USEC;
					while (tmo > 0 && !dev_is_ready())
					{
						delay_us(10);
						tmo -= 10;
					}
					if (tmo <= 0)
					{
						return COMME_TRANS_TIMEOUT;
					}
				}
			}
			shq_spi_rx(p_data_rx, bytes_to_read);
			return (bytes_read + bytes_to_read);
		}
		return COMME_OK;
	}

#endif // STM32
